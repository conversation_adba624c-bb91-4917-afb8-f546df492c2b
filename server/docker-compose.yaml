name: mem0-stack

services:
  # Mem0 API 服务
  mem0-api:
    build:
      context: ..  # 设置上下文为父目录
      dockerfile: server/Dockerfile  # 使用优化的生产 Dockerfile
    container_name: mem0-api
    ports:
      - "${API_PORT:-8000}:8000"
    env_file:
      - .env
    networks:
      - mem0-network
    volumes:
      # 数据持久化
      - mem0-data:/app/data
      # 开发卷挂载
      - .:/app                      # 服务器代码
      - ../mem0:/app/packages/mem0  # Mem0 库
    depends_on:
      qdrant:
        condition: service_started
      neo4j:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "python", "-c", "import urllib.request, sys; urllib.request.urlopen('http://localhost:8000/health', timeout=5); sys.exit(0)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    user: "1000:1000"  # 设置容器运行用户为 1000:1000
    environment:
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONUNBUFFERED=1
      - PYTHONWARNINGS=ignore
      - MEM0_DATA_PATH=/app/data
      - MEM0_DIR=/app/data/.mem0
      - NEO4J_URL=bolt://neo4j:7687
      - NEO4J_USERNAME=neo4j
      - NEO4J_PASSWORD=mem0graph
      - ENABLE_GRAPH_STORE=${ENABLE_GRAPH_STORE:-true}
      - TZ=Asia/Shanghai
    command: uvicorn main:app --host 0.0.0.0 --port 8000
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # Qdrant 向量数据库
  qdrant:
    image: qdrant/qdrant:v1.11.3
    container_name: mem0-qdrant
    networks:
      - mem0-network
    ports:
      - "${QDRANT_PORT:-6333}:6333"
      - "${QDRANT_GRPC_PORT:-6334}:6334"
    volumes:
      - qdrant-data:/qdrant/storage
    restart: unless-stopped
    environment:
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__SERVICE__GRPC_PORT=6334
      - QDRANT__LOG_LEVEL=INFO
      - TZ=Asia/Shanghai
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '0.5'


  # Neo4j 图数据库
  neo4j:
    image: neo4j:5.26.0
    container_name: mem0-neo4j
    networks:
      - mem0-network
    healthcheck:
      test: ["CMD", "cypher-shell", "-u", "neo4j", "-p", "mem0graph", "RETURN 1"]
      interval: 30s
      timeout: 10s
      retries: 10
      start_period: 60s
    ports:
      - "${NEO4J_HTTP_PORT:-7474}:7474"  # HTTP 端口
      - "${NEO4J_BOLT_PORT:-7687}:7687"  # Bolt 端口
    volumes:
      - neo4j-data:/data
      - neo4j-logs:/logs
      - neo4j-import:/var/lib/neo4j/import
      - neo4j-plugins:/plugins
    restart: unless-stopped
    environment:
      - NEO4J_AUTH=${NEO4J_USERNAME:-neo4j}/${NEO4J_PASSWORD:-mem0graph}
      - NEO4J_PLUGINS=["apoc"]
      - NEO4J_apoc_export_file_enabled=true
      - NEO4J_apoc_import_file_enabled=true
      - NEO4J_apoc_import_file_use__neo4j__config=true
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*
      - NEO4J_dbms_security_procedures_allowlist=apoc.*
      - NEO4J_dbms_memory_heap_initial__size=1G
      - NEO4J_dbms_memory_heap_max__size=4G
      - NEO4J_dbms_memory_pagecache_size=2G
      - NEO4J_dbms_logs_debug_level=INFO
      - TZ=Asia/Shanghai
    deploy:
      resources:
        limits:
          memory: 6G
          cpus: '2.0'
        reservations:
          memory: 2G
          cpus: '1.0'

# 数据持久化的命名卷
volumes:
  # Mem0 应用数据
  mem0-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${MEM0_DATA_PATH:-./data}

  # Qdrant 向量数据库
  qdrant-data:
    driver: local

  # Neo4j 图数据库
  neo4j-data:
    driver: local
  neo4j-logs:
    driver: local
  neo4j-import:
    driver: local
  neo4j-plugins:
    driver: local



# 网络配置
networks:
  mem0-network:
    driver: bridge
    name: mem0-network