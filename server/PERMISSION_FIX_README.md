# Mem0 权限问题解决方案

## 问题描述

Mem0 API 服务启动时遇到权限错误：
```
PermissionError: [Errno 13] Permission denied: '/app/data/vector_store'
```

## 根本原因

1. **用户权限不匹配**: Docker 容器内运行的是 `mem0` 用户 (UID 1000)，但宿主机数据目录的所有者是 `root`
2. **目录权限不足**: 数据目录没有正确的读写权限
3. **卷挂载配置**: Docker Compose 卷挂载配置需要优化

## 解决方案

### 1. 修复的文件

#### `docker-compose.yaml`
- 添加 `user: "1000:1000"` 确保容器以正确用户运行
- 修改数据卷路径为相对路径 `./data`

#### `Dockerfile`
- 使用固定的 UID/GID 1000 创建 mem0 用户
- 确保容器内权限设置正确

### 2. 权限修复脚本

#### `fix-permissions.sh`
自动修复数据目录权限：
```bash
./fix-permissions.sh
```

#### `start-services.sh`
完整的服务启动脚本：
```bash
./start-services.sh
```

#### `check-status.sh`
服务状态检查脚本：
```bash
./check-status.sh
```

### 3. 手动修复步骤

如果需要手动修复权限：

```bash
# 1. 停止服务
docker compose down

# 2. 修复权限
sudo chown -R 1000:1000 ./data
chmod -R 755 ./data

# 3. 重新启动
docker compose up --build -d
```

## 验证解决方案

1. **检查服务状态**:
   ```bash
   docker compose ps
   ```

2. **健康检查**:
   ```bash
   curl http://localhost:8000/health
   ```

3. **查看日志**:
   ```bash
   docker compose logs mem0-api
   ```

## 服务端点

- **API 文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health
- **Qdrant 管理界面**: http://localhost:6333/dashboard
- **Neo4j 浏览器**: http://localhost:7474

## 常用命令

```bash
# 启动服务
docker compose up -d

# 查看日志
docker compose logs -f

# 重启服务
docker compose restart

# 停止服务
docker compose down

# 重新构建
docker compose up --build -d
```

## 预防措施

1. **始终使用脚本启动**: 使用 `start-services.sh` 确保权限正确
2. **定期检查**: 使用 `check-status.sh` 监控服务状态
3. **备份数据**: 定期备份 `./data` 目录
4. **权限维护**: 如果手动修改数据目录，记得运行 `fix-permissions.sh`

## 故障排除

如果服务仍然无法启动：

1. 检查 Docker 是否正常运行
2. 确认端口 8000, 6333, 7474, 7687 没有被占用
3. 检查 `.env` 文件配置是否正确
4. 查看完整日志: `docker compose logs`
5. 重新构建镜像: `docker compose build --no-cache`
