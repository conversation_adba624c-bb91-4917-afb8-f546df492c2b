#!/bin/bash

# Mem0 权限修复脚本
# 此脚本确保所有必要的目录具有正确的权限

set -e

echo "🔧 修复 Mem0 数据目录权限..."

# 创建数据目录（如果不存在）
mkdir -p ./data
mkdir -p ./data/.mem0
mkdir -p ./data/vector_store
mkdir -p ./data/history

# 设置正确的所有者和权限
# 使用 1000:1000 对应 Docker 容器内的 mem0 用户
sudo chown -R 1000:1000 ./data
chmod -R 755 ./data

# 确保所有子目录都有正确权限
find ./data -type d -exec chmod 755 {} \;
find ./data -type f -exec chmod 644 {} \;

echo "✅ 权限修复完成！"
echo "📁 数据目录: $(pwd)/data"
echo "👤 所有者: 1000:1000"
echo "🔐 权限: 755 (目录) / 644 (文件)"

# 显示当前权限状态
echo ""
echo "📋 当前权限状态:"
ls -la ./data
