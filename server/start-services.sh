#!/bin/bash

# Mem0 服务启动脚本
# 此脚本确保所有服务正确启动

set -e

echo "🚀 启动 Mem0 服务栈..."

# 检查 Docker 是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker 未运行，请先启动 Docker"
    exit 1
fi

# 修复权限
echo "🔧 检查并修复权限..."
./fix-permissions.sh

# 停止现有服务（如果存在）
echo "🛑 停止现有服务..."
docker compose down --remove-orphans || true

# 清理旧的容器和镜像（可选）
echo "🧹 清理旧资源..."
docker system prune -f || true

# 构建并启动服务
echo "🏗️ 构建并启动服务..."
docker compose up --build -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "📊 检查服务状态..."
docker compose ps

# 检查健康状态
echo "🏥 检查健康状态..."
for i in {1..30}; do
    if curl -f http://localhost:8000/health > /dev/null 2>&1; then
        echo "✅ Mem0 API 服务健康检查通过！"
        break
    fi
    echo "⏳ 等待服务启动... ($i/30)"
    sleep 2
done

# 显示服务信息
echo ""
echo "🎉 Mem0 服务启动完成！"
echo "📡 API 端点: http://localhost:8000"
echo "📚 API 文档: http://localhost:8000/docs"
echo "🏥 健康检查: http://localhost:8000/health"
echo "📊 Qdrant 管理界面: http://localhost:6333/dashboard"
echo "🗄️ Neo4j 浏览器: http://localhost:7474"
echo ""
echo "📋 查看日志: docker compose logs -f"
echo "🛑 停止服务: docker compose down"
