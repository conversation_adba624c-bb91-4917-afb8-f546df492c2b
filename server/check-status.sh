#!/bin/bash

# Mem0 服务状态检查脚本

set -e

echo "🔍 检查 Mem0 服务状态..."
echo ""

# 检查 Docker 服务状态
echo "📊 Docker 容器状态:"
docker compose ps
echo ""

# 检查健康状态
echo "🏥 健康检查:"
if curl -s http://localhost:8000/health | jq . 2>/dev/null; then
    echo "✅ Mem0 API 健康检查通过"
else
    echo "❌ Mem0 API 健康检查失败"
fi
echo ""

# 检查各个服务端点
echo "🌐 服务端点检查:"

# API 端点
if curl -s http://localhost:8000/docs > /dev/null 2>&1; then
    echo "✅ API 文档: http://localhost:8000/docs"
else
    echo "❌ API 文档不可访问"
fi

# Qdrant 端点
if curl -s http://localhost:6333/dashboard > /dev/null 2>&1; then
    echo "✅ Qdrant 管理界面: http://localhost:6333/dashboard"
else
    echo "❌ Qdrant 管理界面不可访问"
fi

# Neo4j 端点
if curl -s http://localhost:7474 > /dev/null 2>&1; then
    echo "✅ Neo4j 浏览器: http://localhost:7474"
else
    echo "❌ Neo4j 浏览器不可访问"
fi

echo ""

# 检查数据目录权限
echo "📁 数据目录权限:"
ls -la ./data
echo ""

# 显示最近的日志
echo "📋 最近的服务日志:"
docker compose logs --tail=5 mem0-api
echo ""

echo "🎉 状态检查完成！"
echo ""
echo "💡 有用的命令:"
echo "   查看完整日志: docker compose logs -f"
echo "   重启服务: docker compose restart"
echo "   停止服务: docker compose down"
echo "   重新构建: docker compose up --build -d"
