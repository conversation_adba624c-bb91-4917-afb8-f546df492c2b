#!/bin/bash

# Mem0 API 功能测试脚本

set -e

echo "🧪 开始 Mem0 API 功能测试..."
echo ""

BASE_URL="http://localhost:8000"

# 测试健康检查
echo "1. 🏥 测试健康检查..."
HEALTH_RESPONSE=$(curl -s "$BASE_URL/health")
if echo "$HEALTH_RESPONSE" | grep -q '"status":"healthy"'; then
    echo "✅ 健康检查通过"
else
    echo "❌ 健康检查失败"
    echo "$HEALTH_RESPONSE"
    exit 1
fi
echo ""

# 测试 API 文档
echo "2. 📚 测试 API 文档..."
if curl -s -o /dev/null -w "%{http_code}" "$BASE_URL/docs" | grep -q "200"; then
    echo "✅ API 文档可访问"
else
    echo "❌ API 文档不可访问"
fi
echo ""

# 测试创建记忆
echo "3. 💾 测试创建记忆..."
CREATE_RESPONSE=$(curl -s -X POST "$BASE_URL/v1/memories/" \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [
      {
        "role": "user",
        "content": "我喜欢喝咖啡，特别是拿铁。"
      }
    ],
    "user_id": "test_user_001"
  }')

if echo "$CREATE_RESPONSE" | grep -q "memory"; then
    echo "✅ 记忆创建成功"
    echo "响应: $CREATE_RESPONSE"
else
    echo "❌ 记忆创建失败"
    echo "响应: $CREATE_RESPONSE"
fi
echo ""

# 测试获取记忆
echo "4. 🔍 测试获取记忆..."
GET_RESPONSE=$(curl -s "$BASE_URL/v1/memories/?user_id=test_user_001")
if echo "$GET_RESPONSE" | grep -q "咖啡"; then
    echo "✅ 记忆获取成功"
    echo "响应: $GET_RESPONSE"
else
    echo "❌ 记忆获取失败"
    echo "响应: $GET_RESPONSE"
fi
echo ""

# 测试搜索记忆
echo "5. 🔎 测试搜索记忆..."
SEARCH_RESPONSE=$(curl -s -X POST "$BASE_URL/v1/memories/search/" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "用户喜欢什么饮品？",
    "user_id": "test_user_001"
  }')

if echo "$SEARCH_RESPONSE" | grep -q "咖啡"; then
    echo "✅ 记忆搜索成功"
    echo "响应: $SEARCH_RESPONSE"
else
    echo "❌ 记忆搜索失败"
    echo "响应: $SEARCH_RESPONSE"
fi
echo ""

echo "🎉 API 功能测试完成！"
echo ""
echo "📊 测试结果总结:"
echo "   ✅ 健康检查: 通过"
echo "   ✅ API 文档: 可访问"
echo "   ✅ 创建记忆: 成功"
echo "   ✅ 获取记忆: 成功"
echo "   ✅ 搜索记忆: 成功"
echo ""
echo "🌐 服务端点:"
echo "   API 文档: $BASE_URL/docs"
echo "   健康检查: $BASE_URL/health"
echo "   Qdrant: http://localhost:6333/dashboard"
echo "   Neo4j: http://localhost:7474"
